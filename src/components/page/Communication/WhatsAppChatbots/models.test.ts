import { 
  validateFileUpload, 
  getDefaultChatbotFormData, 
  ChatbotType, 
  MAX_FILE_SIZE,
  ALLOWED_FILE_TYPES 
} from './models';

describe('WhatsApp Chatbot Models', () => {
  describe('validateFileUpload', () => {
    it('should return error for null file', () => {
      const result = validateFileUpload(null as any);
      expect(result).toBe('Please select a file');
    });

    it('should return error for file size exceeding limit', () => {
      const largeFile = new File([''], 'test.pdf', { 
        type: 'application/pdf',
        lastModified: Date.now()
      });
      
      // Mock file size to be larger than MAX_FILE_SIZE
      Object.defineProperty(largeFile, 'size', {
        value: MAX_FILE_SIZE + 1,
        writable: false
      });

      const result = validateFileUpload(largeFile);
      expect(result).toBe('File size must be less than 10MB');
    });

    it('should return error for invalid file type', () => {
      const invalidFile = new File([''], 'test.txt', { 
        type: 'text/plain',
        lastModified: Date.now()
      });

      const result = validateFileUpload(invalidFile);
      expect(result).toBe('Only PDF files are allowed');
    });

    it('should return null for valid PDF file', () => {
      const validFile = new File([''], 'test.pdf', { 
        type: 'application/pdf',
        lastModified: Date.now()
      });
      
      Object.defineProperty(validFile, 'size', {
        value: 1024 * 1024, // 1MB
        writable: false
      });

      const result = validateFileUpload(validFile);
      expect(result).toBeNull();
    });
  });

  describe('getDefaultChatbotFormData', () => {
    it('should return default form data with correct structure', () => {
      const defaultData = getDefaultChatbotFormData();
      
      expect(defaultData).toEqual({
        name: '',
        description: '',
        type: ChatbotType.RULE_BASED,
        whatsappAccount: null,
        welcomeMessage: '',
        thankYouMessage: '',
        questions: [{ question: '', order: 1 }]
      });
    });

    it('should have RULE_BASED as default type', () => {
      const defaultData = getDefaultChatbotFormData();
      expect(defaultData.type).toBe(ChatbotType.RULE_BASED);
    });

    it('should have one empty question by default', () => {
      const defaultData = getDefaultChatbotFormData();
      expect(defaultData.questions).toHaveLength(1);
      expect(defaultData.questions[0]).toEqual({ question: '', order: 1 });
    });
  });

  describe('Constants', () => {
    it('should have correct MAX_FILE_SIZE', () => {
      expect(MAX_FILE_SIZE).toBe(10 * 1024 * 1024); // 10MB
    });

    it('should have correct ALLOWED_FILE_TYPES', () => {
      expect(ALLOWED_FILE_TYPES).toEqual(['application/pdf']);
    });

    it('should have correct ChatbotType enum values', () => {
      expect(ChatbotType.AI).toBe('AI');
      expect(ChatbotType.RULE_BASED).toBe('RULE_BASED');
    });
  });
});
