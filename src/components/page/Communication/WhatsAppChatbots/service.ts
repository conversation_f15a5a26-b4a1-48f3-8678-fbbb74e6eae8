import * as api from '../../../../services/api';
import { defaultApiConfig, generateBaseUrl } from '../../../../config/apiConfig';
import {
  ChatbotQuestion,
  ChatbotApiResponse,
  EntityField
} from './models';

const getBaseUrl = () => generateBaseUrl(defaultApiConfig, '');

// Fetch entity fields for lead entity (GET /v1/entities/lead/fields?entityType=lead)
export const getEntityFields = (entityType: string = 'lead'): Promise<EntityField[]> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/entities/${entityType}/fields?entityType=${entityType}`;

  console.log('Making HTTP GET request to:', url);
  console.log('Request headers:', headers);

  const requestConfig = {
    url,
    headers,
    method: 'get'
  };

  return api.callAPI(requestConfig)
    .then(response => {
      console.log('HTTP GET entity fields response received:', response);
      // Filter out internal fields and return only external TEXT_FIELD fields
      const fields = response.data || [];
      return fields.filter((field: EntityField) => !field.internal && field.type === 'TEXT_FIELD');
    })
    .catch(error => {
      console.error('HTTP GET entity fields request failed:', error);
      throw error;
    });
};

// Step 1: Create chatbot basic information (POST /v1/chatbot)
export const createChatbotStep1 = (step1Data: {
  name: string;
  type: string;
  description: string;
  welcomeMessage: string;
  thankYouMessage: string;
}): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot`;

  const payload = {
    name: step1Data.name,
    type: step1Data.type,
    description: step1Data.description,
    welcomeMessage: step1Data.welcomeMessage,
    thankYouMessage: step1Data.thankYouMessage
  };

  console.log('Making HTTP POST request to:', url);
  console.log('Request payload:', payload);
  console.log('Request headers:', headers);

  const requestConfig = {
    url,
    headers,
    method: 'post',
    data: payload
  };

  return api.callAPI(requestConfig)
    .then(response => {
      console.log('HTTP POST response received:', response);
      // Map axios response to expected ChatbotApiResponse format
      return {
        data: response.data,
        message: response.data?.message || 'Chatbot created successfully',
        success: response.status >= 200 && response.status < 300
      };
    })
    .catch(error => {
      console.error('HTTP POST request failed:', error);
      throw error;
    });
};

// Step 2: Submit questions to chatbot (POST /chatbot/{chatbotId}/questions)
export const submitChatbotQuestions = (chatbotId: string, questions: ChatbotQuestion[]): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot/${chatbotId}/questions`;

  // Filter out empty questions and prepare payload
  const validQuestions = questions.filter(q => q.question && q.question.trim() !== '');
  const payload = validQuestions.map((question, index) => ({
    question: question.question.trim(),
    fieldId: question.fieldId
  }));

  console.log('Making HTTP POST request to:', url);
  console.log('Questions payload:', payload);
  console.log('Request headers:', headers);

  const requestConfig = {
    url,
    headers,
    method: 'post',
    data: payload
  };

  return api.callAPI(requestConfig)
    .then(response => {
      console.log('HTTP POST questions response received:', response);
      // Map axios response to expected ChatbotApiResponse format
      return {
        data: response.data,
        message: response.data?.message || 'Questions submitted successfully',
        success: response.status >= 200 && response.status < 300
      };
    })
    .catch(error => {
      console.error('HTTP POST questions request failed:', error);
      throw error;
    });
};

// Step 3: Upload knowledge base to chatbot (POST /v1/chatbot/{id}/knowledgebase)
export const uploadKnowledgeBase = (chatbotId: string, files: File[]): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot/${chatbotId}/knowledgebase`;

  console.log('Making HTTP POST request to:', url);
  console.log('Uploading files:', files.map(f => f.name));
  console.log('Request headers:', headers);

  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });

  return api.callAPI({
    url,
    headers,
    method: 'post',
    data: formData
  })
    .then(response => {
      console.log('HTTP POST knowledge base response received:', response);
      return {
        data: response.data,
        message: 'Knowledge base uploaded successfully',
        success: true
      };
    })
    .catch(error => {
      console.error('HTTP POST knowledge base request failed:', error);
      return {
        data: null,
        message: `Failed to upload knowledge base: ${error.message}`,
        success: false
      };
    });
};

// Step 4: Publish chatbot (POST /v1/chatbot/{id}/publish)
export const publishChatbot = (chatbotId: string): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot/${chatbotId}/publish`;

  console.log('Making HTTP POST request to:', url);
  console.log('Publishing chatbot ID:', chatbotId);
  console.log('Request headers:', headers);

  return api.callAPI({
    url,
    headers,
    method: 'post'
  })
    .then(response => {
      console.log('HTTP POST publish chatbot response received:', response);
      return {
        data: response.data,
        message: 'Chatbot published successfully',
        success: true
      };
    })
    .catch(error => {
      console.error('HTTP POST publish chatbot request failed:', error);
      return {
        data: null,
        message: `Failed to publish chatbot: ${error.message}`,
        success: false
      };
    });
};
