export enum ChatbotType {
  AI = 'AI',
  RULE_BASED = 'RULE_BASED'
}

export interface ChatbotQuestion {
  id?: string;
  question: string;
  order: number;
}

export interface KnowledgeBase {
  id?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedAt?: string;
  fileUrl?: string;
}

export interface WhatsAppChatbot {
  id?: string;
  tenant_id?: string;
  name: string;
  description: string;
  type: ChatbotType;
  welcomeMessage: string;
  thankYouMessage: string;
  // API response uses snake_case
  welcome_message?: string;
  thank_you_message?: string;
  connected_account_id?: string;
  status?: string;
  questions: ChatbotQuestion[];
  knowledgeBase?: KnowledgeBase;
  knowledgebase_ids?: string[];
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  // API response uses snake_case
  created_at?: string;
  updated_at?: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface WhatsAppAccount {
  id: number;
  name: string;
}

export interface ChatbotFormData {
  name: string;
  description: string;
  type: ChatbotType;
  whatsappAccount: WhatsAppAccount;
  welcomeMessage: string;
  thankYouMessage: string;
  questions: ChatbotQuestion[];
  knowledgeBaseFile?: File;
}

export interface ChatbotListResponse {
  content: WhatsAppChatbot[];
  totalElements: number;
  totalPages: number;
  page: {
    no: number;
    size: number;
  };
}

export interface ChatbotApiResponse {
  data: WhatsAppChatbot;
  message: string;
  success: boolean;
}

export interface ChatbotListApiResponse {
  data: ChatbotListResponse;
  message: string;
  success: boolean;
}

export interface FileUploadResponse {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
}

export const CHATBOT_FORM_NAME = 'CHATBOT_FORM';

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
export const ALLOWED_FILE_TYPES = ['application/pdf'];

export const validateFileUpload = (file: File): string | null => {
  if (!file) {
    return 'Please select a file';
  }
  
  if (file.size > MAX_FILE_SIZE) {
    return 'File size must be less than 10MB';
  }
  
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return 'Only PDF files are allowed';
  }
  
  return null;
};

export const getDefaultChatbotFormData = (): ChatbotFormData => ({
  name: '',
  description: '',
  type: ChatbotType.RULE_BASED,
  whatsappAccount: null,
  welcomeMessage: '',
  thankYouMessage: '',
  questions: [{ question: '', order: 1 }]
});
